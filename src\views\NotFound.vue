<template>
  <div class="not-found">
    <div class="error-content">
      <h1 class="error-code">404</h1>
      <h2 class="error-title">页面未找到</h2>
      <p class="error-message">抱歉，您访问的页面不存在或已被移除。</p>
      <router-link to="/" class="home-button">
        返回首页
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
// 404页面逻辑
</script>

<style scoped>
.not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--spacing-xl);
  background-color: var(--bg-secondary);
}

.error-content {
  text-align: center;
  max-width: 500px;
  background-color: var(--bg-primary);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
}

.error-code {
  font-size: 8rem;
  font-weight: bold;
  color: var(--accent-color);
  margin: 0;
  line-height: 1;
}

.error-title {
  font-size: var(--font-xxxl);
  color: var(--text-primary);
  margin: var(--spacing-lg) 0;
  font-weight: 600;
}

.error-message {
  font-size: var(--font-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

.home-button {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-xl);
  background-color: var(--primary-color);
  color: var(--text-white);
  text-decoration: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  font-size: var(--font-md);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.home-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

@media (max-width: 768px) {
  .not-found {
    padding: var(--spacing-md);
  }

  .error-content {
    padding: var(--spacing-xl);
  }

  .error-code {
    font-size: 6rem;
  }

  .error-title {
    font-size: var(--font-xxl);
  }
}
</style>
