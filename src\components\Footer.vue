<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <!-- 左侧信息 -->
        <div class="footer-info">
          <div class="footer-logo">
            <img 
              src="https://picsum.photos/32/32?random=2" 
              alt="Logo" 
              class="footer-logo-image"
            />
            <span class="footer-logo-text">量化积分管理系统</span>
          </div>
          <p class="footer-description">
            基于 Vue 3 + TypeScript + Vite 构建的现代化积分管理平台
          </p>
        </div>

        <!-- 中间链接 -->
        <div class="footer-links">
          <div class="footer-section">
            <h4 class="footer-section-title">产品</h4>
            <ul class="footer-link-list">
              <li><a href="#" class="footer-link">积分中心</a></li>
              <li><a href="#" class="footer-link">排行榜</a></li>
              <li><a href="#" class="footer-link">数据统计</a></li>
              <li><a href="#" class="footer-link">系统设置</a></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4 class="footer-section-title">支持</h4>
            <ul class="footer-link-list">
              <li><a href="#" class="footer-link">帮助文档</a></li>
              <li><a href="#" class="footer-link">常见问题</a></li>
              <li><a href="#" class="footer-link">联系我们</a></li>
              <li><a href="#" class="footer-link">意见反馈</a></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4 class="footer-section-title">关于</h4>
            <ul class="footer-link-list">
              <li><a href="#" class="footer-link">关于我们</a></li>
              <li><a href="#" class="footer-link">隐私政策</a></li>
              <li><a href="#" class="footer-link">服务条款</a></li>
              <li><a href="#" class="footer-link">更新日志</a></li>
            </ul>
          </div>
        </div>

        <!-- 右侧联系方式 -->
        <div class="footer-contact">
          <h4 class="footer-section-title">联系方式</h4>
          <div class="contact-item">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            <span><EMAIL></span>
          </div>
          <div class="contact-item">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
            </svg>
            <span>400-123-4567</span>
          </div>
          <div class="contact-item">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
            <span>北京市朝阳区</span>
          </div>
        </div>
      </div>

      <!-- 底部版权信息 -->
      <div class="footer-bottom">
        <div class="footer-copyright">
          <p>&copy; 2025 量化积分管理系统. All rights reserved.</p>
        </div>
        <div class="footer-tech">
          <span class="tech-badge">Vue 3</span>
          <span class="tech-badge">TypeScript</span>
          <span class="tech-badge">Vite</span>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
// Footer组件逻辑
</script>

<style scoped>
.footer {
  background-color: var(--bg-dark);
  color: var(--text-white);
  margin-top: auto;
}

.footer-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: var(--spacing-xxl) var(--spacing-xl) var(--spacing-xl);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: var(--spacing-xxl);
  margin-bottom: var(--spacing-xl);
}

/* 左侧信息 */
.footer-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.footer-logo-image {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.footer-logo-text {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-white);
}

.footer-description {
  color: var(--text-light);
  font-size: var(--font-sm);
  line-height: 1.6;
  max-width: 280px;
}

/* 中间链接 */
.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
}

.footer-section-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-white);
  margin-bottom: var(--spacing-md);
}

.footer-link-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-link {
  color: var(--text-light);
  text-decoration: none;
  font-size: var(--font-sm);
  transition: color var(--transition-normal);
}

.footer-link:hover {
  color: var(--primary-color);
}

/* 右侧联系方式 */
.footer-contact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-light);
  font-size: var(--font-sm);
}

.contact-item svg {
  flex-shrink: 0;
  color: var(--primary-color);
}

/* 底部版权信息 */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-xl);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-copyright {
  color: var(--text-light);
  font-size: var(--font-sm);
}

.footer-tech {
  display: flex;
  gap: var(--spacing-sm);
}

.tech-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: rgba(52, 152, 219, 0.2);
  color: var(--primary-color);
  border-radius: var(--border-radius);
  font-size: var(--font-xs);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .footer-container {
    padding: var(--spacing-xl) var(--spacing-md) var(--spacing-md);
  }
  
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
  }
  
  .footer-contact {
    grid-column: 1 / -1;
    margin-top: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .footer-container {
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
  }
  
  .footer-links {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .footer-logo-text {
    font-size: var(--font-md);
  }
  
  .footer-tech {
    justify-content: center;
  }
}
</style>
