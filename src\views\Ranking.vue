<template>
  <div class="ranking-page">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">排行榜</h1>
        <p class="page-subtitle">查看积分排名和统计信息</p>
      </div>
      
      <div class="page-content">
        <div class="placeholder-card">
          <div class="placeholder-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <path d="M6 9l6 6 6-6"></path>
            </svg>
          </div>
          <h3>排行榜功能开发中</h3>
          <p>此页面将展示学生积分排行榜、班级排名、历史排名变化等信息。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 排行榜页面逻辑
</script>

<style scoped>
.ranking-page {
  min-height: calc(100vh - var(--header-height));
  background-color: var(--bg-secondary);
}

.page-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: var(--spacing-xl) var(--container-padding);
  width: 100%;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
}

.page-title {
  font-size: var(--font-xxxl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-lg);
  color: var(--text-secondary);
}

.page-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.placeholder-card {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xxl);
  text-align: center;
  box-shadow: var(--shadow-md);
  max-width: 500px;
}

.placeholder-icon {
  color: var(--success-color);
  margin-bottom: var(--spacing-lg);
}

.placeholder-card h3 {
  font-size: var(--font-xl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.placeholder-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-md) var(--container-padding);
  }

  .placeholder-card {
    padding: var(--spacing-xl);
  }
}
</style>
