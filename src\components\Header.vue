<template>
  <header class="header">
    <div class="header-container">
      <!-- Logo区域 -->
      <div class="header-logo">
        <router-link to="/" class="logo-link">
          <img 
            src="https://picsum.photos/40/40?random=1" 
            alt="Logo" 
            class="logo-image"
          />
          <span class="logo-text">量化积分管理系统</span>
        </router-link>
      </div>

      <!-- 导航菜单 -->
      <nav class="header-nav">
        <ul class="nav-list">
          <li class="nav-item">
            <router-link to="/" class="nav-link" active-class="nav-link-active">
              主页
            </router-link>
          </li>
          <li class="nav-item">
            <router-link 
              :to="userRole === 'student' ? '/points' : '/admin'" 
              class="nav-link" 
              active-class="nav-link-active"
            >
              {{ userRole === 'student' ? '积分中心' : '管理中心' }}
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/ranking" class="nav-link" active-class="nav-link-active">
              排行榜
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/statistics" class="nav-link" active-class="nav-link-active">
              数据统计
            </router-link>
          </li>
          <li v-if="userRole === 'admin'" class="nav-item">
            <router-link to="/settings" class="nav-link" active-class="nav-link-active">
              系统设置
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/help" class="nav-link" active-class="nav-link-active">
              帮助文档
            </router-link>
          </li>
        </ul>
      </nav>

      <!-- 右侧功能区 -->
      <div class="header-actions">
        <!-- 搜索框 -->
        <div class="search-box">
          <input 
            type="text" 
            placeholder="搜索..." 
            class="search-input"
            v-model="searchQuery"
            @keyup.enter="handleSearch"
          />
          <button class="search-btn" @click="handleSearch">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
        </div>

        <!-- 角色切换按钮 -->
        <button class="role-toggle-btn" @click="toggleRole">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          {{ userRole === 'student' ? '学生' : '管理员' }}
        </button>

        <!-- 登录/退出按钮 -->
        <button v-if="!isLoggedIn" class="login-btn btn btn-primary" @click="handleLogin">
          登录
        </button>
        <div v-else class="user-menu">
          <button class="user-btn" @click="toggleUserMenu">
            <span>{{ username }}</span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="6,9 12,15 18,9"></polyline>
            </svg>
          </button>
          <div v-if="showUserMenu" class="user-dropdown">
            <a href="#" class="dropdown-item" @click="handleProfile">个人中心</a>
            <a href="#" class="dropdown-item" @click="handleLogout">退出登录</a>
          </div>
        </div>
      </div>

      <!-- 移动端菜单按钮 -->
      <button class="mobile-menu-btn" @click="toggleMobileMenu">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="3" y1="6" x2="21" y2="6"></line>
          <line x1="3" y1="12" x2="21" y2="12"></line>
          <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
      </button>
    </div>

    <!-- 移动端菜单 -->
    <div v-if="showMobileMenu" class="mobile-menu">
      <nav class="mobile-nav">
        <router-link to="/" class="mobile-nav-link" @click="closeMobileMenu">主页</router-link>
        <router-link 
          :to="userRole === 'student' ? '/points' : '/admin'" 
          class="mobile-nav-link" 
          @click="closeMobileMenu"
        >
          {{ userRole === 'student' ? '积分中心' : '管理中心' }}
        </router-link>
        <router-link to="/ranking" class="mobile-nav-link" @click="closeMobileMenu">排行榜</router-link>
        <router-link to="/statistics" class="mobile-nav-link" @click="closeMobileMenu">数据统计</router-link>
        <router-link v-if="userRole === 'admin'" to="/settings" class="mobile-nav-link" @click="closeMobileMenu">
          系统设置
        </router-link>
        <router-link to="/help" class="mobile-nav-link" @click="closeMobileMenu">帮助文档</router-link>
      </nav>
      <div class="mobile-actions">
        <button class="mobile-role-btn" @click="toggleRole">
          切换到{{ userRole === 'student' ? '管理员' : '学生' }}
        </button>
        <button v-if="!isLoggedIn" class="mobile-login-btn" @click="handleLogin">登录</button>
        <button v-else class="mobile-logout-btn" @click="handleLogout">退出登录</button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const userRole = ref<'student' | 'admin'>('student')
const isLoggedIn = ref(false)
const username = ref('用户名')
const showUserMenu = ref(false)
const showMobileMenu = ref(false)

// 方法
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    console.log('搜索:', searchQuery.value)
    // TODO: 实现搜索功能
  }
}

const toggleRole = () => {
  userRole.value = userRole.value === 'student' ? 'admin' : 'student'
  console.log('角色切换到:', userRole.value)
  // TODO: 实现角色切换逻辑
}

const handleLogin = () => {
  console.log('登录')
  // TODO: 实现登录逻辑
  isLoggedIn.value = true
}

const handleLogout = () => {
  console.log('退出登录')
  // TODO: 实现退出登录逻辑
  isLoggedIn.value = false
  showUserMenu.value = false
  closeMobileMenu()
}

const handleProfile = () => {
  console.log('个人中心')
  showUserMenu.value = false
  // TODO: 跳转到个人中心
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

// 点击外部关闭用户菜单
document.addEventListener('click', (e) => {
  const target = e.target as HTMLElement
  if (!target.closest('.user-menu')) {
    showUserMenu.value = false
  }
})
</script>

<style scoped>
.header {
  background-color: var(--bg-primary);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: var(--header-height);
}

.header-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* Logo区域 */
.header-logo {
  flex-shrink: 0;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-primary);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.logo-link:hover {
  color: var(--primary-color);
}

.logo-image {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.logo-text {
  font-size: var(--font-xl);
  font-weight: 600;
  white-space: nowrap;
}

/* 导航菜单 */
.header-nav {
  flex: 1;
  margin: 0 var(--spacing-xl);
  display: flex;
  justify-content: center;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
  margin: 0;
  padding: 0;
  align-items: center;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: all var(--transition-normal);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
  background-color: var(--bg-secondary);
}

.nav-link-active {
  color: var(--primary-color);
}

.nav-link-active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

/* 右侧功能区 */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-shrink: 0;
}

/* 搜索框 */
.search-box {
  display: flex;
  align-items: center;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: border-color var(--transition-normal);
}

.search-box:focus-within {
  border-color: var(--primary-color);
}

.search-input {
  border: none;
  outline: none;
  padding: var(--spacing-sm) var(--spacing-md);
  background: transparent;
  font-size: var(--font-sm);
  width: 200px;
  color: var(--text-primary);
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.search-btn {
  border: none;
  background: none;
  padding: var(--spacing-sm);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color var(--transition-normal);
}

.search-btn:hover {
  color: var(--primary-color);
}

/* 角色切换按钮 */
.role-toggle-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: var(--border-radius);
  font-size: var(--font-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.role-toggle-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* 登录按钮 */
.login-btn {
  font-size: var(--font-sm);
}

/* 用户菜单 */
.user-menu {
  position: relative;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-radius: var(--border-radius);
  font-size: var(--font-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.user-btn:hover {
  border-color: var(--primary-color);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--spacing-xs);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  min-width: 120px;
  z-index: 1001;
}

.dropdown-item {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  text-decoration: none;
  font-size: var(--font-sm);
  transition: background-color var(--transition-normal);
}

.dropdown-item:hover {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  border: none;
  background: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--spacing-xs);
}

/* 移动端菜单 */
.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  z-index: 999;
}

.mobile-nav {
  padding: var(--spacing-md);
}

.mobile-nav-link {
  display: block;
  padding: var(--spacing-md);
  color: var(--text-primary);
  text-decoration: none;
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-normal);
}

.mobile-nav-link:hover {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
}

.mobile-nav-link:last-child {
  border-bottom: none;
}

.mobile-actions {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.mobile-role-btn,
.mobile-login-btn,
.mobile-logout-btn {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.mobile-login-btn {
  background-color: var(--primary-color);
  color: var(--text-white);
  border-color: var(--primary-color);
}

.mobile-role-btn:hover,
.mobile-logout-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.mobile-login-btn:hover {
  background-color: var(--primary-dark);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .nav-list {
    gap: var(--spacing-lg);
  }

  .search-input {
    width: 180px;
  }
}

@media (max-width: 1024px) {
  .header-container {
    padding: 0 var(--container-padding);
  }

  .search-input {
    width: 150px;
  }

  .nav-list {
    gap: var(--spacing-md);
  }

  .header-nav {
    margin: 0 var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .header-nav,
  .search-box,
  .role-toggle-btn,
  .login-btn,
  .user-menu {
    display: none;
  }

  .mobile-menu-btn {
    display: block;
  }

  .mobile-menu {
    display: block;
  }

  .logo-text {
    font-size: var(--font-lg);
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 var(--spacing-md);
  }

  .logo-text {
    display: none;
  }

  .logo-image {
    width: 32px;
    height: 32px;
  }
}
</style>
