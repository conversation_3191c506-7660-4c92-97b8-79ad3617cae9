<template>
  <div class="help-page">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">帮助文档</h1>
        <p class="page-subtitle">使用指南和常见问题解答</p>
      </div>
      
      <div class="page-content">
        <div class="placeholder-card">
          <div class="placeholder-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
          </div>
          <h3>帮助文档功能开发中</h3>
          <p>此页面将提供系统使用指南、常见问题解答、操作教程等帮助信息。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 帮助文档页面逻辑
</script>

<style scoped>
.help-page {
  min-height: calc(100vh - var(--header-height));
  background-color: var(--bg-secondary);
}

.page-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: var(--spacing-xl) var(--container-padding);
  width: 100%;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
}

.page-title {
  font-size: var(--font-xxxl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-lg);
  color: var(--text-secondary);
}

.page-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.placeholder-card {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xxl);
  text-align: center;
  box-shadow: var(--shadow-md);
  max-width: 500px;
}

.placeholder-icon {
  color: var(--accent-color);
  margin-bottom: var(--spacing-lg);
}

.placeholder-card h3 {
  font-size: var(--font-xl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.placeholder-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-md) var(--container-padding);
  }

  .placeholder-card {
    padding: var(--spacing-xl);
  }
}
</style>
