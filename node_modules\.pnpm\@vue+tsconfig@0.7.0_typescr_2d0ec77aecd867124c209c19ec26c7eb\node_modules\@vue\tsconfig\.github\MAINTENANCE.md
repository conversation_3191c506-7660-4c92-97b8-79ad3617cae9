This document explains how to perform the project's maintenance tasks.

### Creating a new release

Anyone with write access to the repository can request a new release. To do so, follow these steps:

1. Run `npm version <patch|minor|major>` locally to bump the version number and create a new commit / tag.
2. Push the commit and tag to the repository by running `git push --follow-tags`.
3. The release will be automatically published to npm by GitHub Actions once approved by an administrator.
4. Go to <https://github.com/vuejs/tsconfig/releases/new> and create a new release with the tag that was just created. Describe the notable changes in the release notes.
