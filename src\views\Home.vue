<template>
  <div class="home">
    <div class="hero">
      <h1>欢迎使用量化积分管理系统</h1>
      <p class="subtitle">基于 Vue 3 + TypeScript + Vite 构建的现代化前端应用</p>
    </div>
    
    <div class="features">
      <div class="feature-card">
        <h3>🚀 现代化技术栈</h3>
        <p>Vue 3 + TypeScript + Vite</p>
      </div>
      <div class="feature-card">
        <h3>📱 响应式设计</h3>
        <p>适配PC端的响应式布局</p>
      </div>
      <div class="feature-card">
        <h3>⚡ 快速开发</h3>
        <p>热重载和快速构建</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 首页逻辑
</script>

<style scoped>
.home {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: var(--spacing-xl);
  min-height: calc(100vh - var(--header-height));
  background-color: var(--bg-secondary);
}

.hero {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
  padding: var(--spacing-xxl) 0;
}

.hero h1 {
  font-size: var(--font-xxxl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: 700;
}

.subtitle {
  font-size: var(--font-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.feature-card {
  background: var(--bg-primary);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-color);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.feature-card h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-xl);
  font-weight: 600;
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

@media (max-width: 768px) {
  .home {
    padding: var(--spacing-md);
  }

  .hero {
    padding: var(--spacing-xl) 0;
  }

  .hero h1 {
    font-size: var(--font-xxl);
  }

  .features {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .feature-card {
    padding: var(--spacing-lg);
  }
}
</style>
