<template>
  <div class="home">
    <div class="hero">
      <h1>欢迎使用量化积分管理系统</h1>
      <p class="subtitle">基于 Vue 3 + TypeScript + Vite 构建的现代化前端应用</p>
    </div>
    
    <div class="features">
      <div class="feature-card">
        <h3>🚀 现代化技术栈</h3>
        <p>Vue 3 + TypeScript + Vite</p>
      </div>
      <div class="feature-card">
        <h3>📱 响应式设计</h3>
        <p>适配PC端的响应式布局</p>
      </div>
      <div class="feature-card">
        <h3>⚡ 快速开发</h3>
        <p>热重载和快速构建</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 首页逻辑
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.hero {
  text-align: center;
  margin-bottom: 4rem;
}

.hero h1 {
  font-size: 3rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin-bottom: 2rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.feature-card p {
  color: #7f8c8d;
}

@media (max-width: 768px) {
  .hero h1 {
    font-size: 2rem;
  }
  
  .features {
    grid-template-columns: 1fr;
  }
}
</style>
