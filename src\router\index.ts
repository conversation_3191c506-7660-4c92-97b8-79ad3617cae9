import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '../layouts/Layout.vue'
import Home from '../views/Home.vue'
import NotFound from '../views/NotFound.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Home',
        component: Home,
        meta: { title: '主页' }
      },
      {
        path: 'points',
        name: 'Points',
        component: () => import('../views/Points.vue'),
        meta: { title: '积分中心', requiresAuth: true }
      },
      {
        path: 'admin',
        name: 'Admin',
        component: () => import('../views/Admin.vue'),
        meta: { title: '管理中心', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'ranking',
        name: 'Ranking',
        component: () => import('../views/Ranking.vue'),
        meta: { title: '排行榜' }
      },
      {
        path: 'statistics',
        name: 'Statistics',
        component: () => import('../views/Statistics.vue'),
        meta: { title: '数据统计' }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('../views/Settings.vue'),
        meta: { title: '系统设置', requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'help',
        name: 'Help',
        component: () => import('../views/Help.vue'),
        meta: { title: '帮助文档' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { title: '页面未找到' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 量化积分管理系统`
  } else {
    document.title = '量化积分管理系统'
  }

  // TODO: 实现身份验证逻辑
  // 这里暂时跳过身份验证，后续可以添加
  /*
  if (to.meta.requiresAuth) {
    // 检查用户是否已登录
    const isLoggedIn = false // 从状态管理或本地存储获取
    if (!isLoggedIn) {
      next('/login')
      return
    }
  }

  if (to.meta.requiresAdmin) {
    // 检查用户是否为管理员
    const isAdmin = false // 从状态管理或本地存储获取
    if (!isAdmin) {
      next('/')
      return
    }
  }
  */

  next()
})

export default router
