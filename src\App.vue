<script setup lang="ts">
// App 根组件
</script>

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<style>
:root {
  /* 主题色彩变量 */
  --primary-color: #3498db;
  --primary-dark: #2980b9;
  --secondary-color: #2c3e50;
  --accent-color: #e74c3c;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --info-color: #17a2b8;

  /* 文字颜色 */
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --text-white: #ffffff;

  /* 背景颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-light: #ecf0f1;
  --bg-dark: #2c3e50;

  /* 边框和阴影 */
  --border-color: #dee2e6;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 2px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);

  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;

  /* 字体大小 */
  --font-xs: 0.75rem;
  --font-sm: 0.875rem;
  --font-md: 1rem;
  --font-lg: 1.125rem;
  --font-xl: 1.25rem;
  --font-xxl: 1.5rem;
  --font-xxxl: 2rem;

  /* 布局尺寸 */
  --header-height: 60px;
  --max-width: 1400px;
  --container-padding: 2rem;
  --border-radius: 6px;
  --border-radius-lg: 8px;

  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  font-size: var(--font-md);
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-light);
}

::-webkit-scrollbar-thumb {
  background: var(--text-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 全局链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-normal);
}

a:hover {
  color: var(--primary-dark);
}

/* 全局按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--bg-light);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-primary);
}

/* 响应式断点 */
@media (max-width: 1440px) {
  :root {
    --max-width: 100%;
    --container-padding: 1.5rem;
  }
}

@media (max-width: 1024px) {
  :root {
    --container-padding: 1rem;
  }
}

@media (max-width: 768px) {
  :root {
    --spacing-xl: 1rem;
    --spacing-xxl: 1.5rem;
    --font-xxl: 1.25rem;
    --font-xxxl: 1.5rem;
  }
}

@media (max-width: 480px) {
  :root {
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
    --spacing-xl: 1rem;
  }
}
</style>
