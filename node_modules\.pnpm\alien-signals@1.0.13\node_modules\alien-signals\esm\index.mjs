var SubscriberFlags=(SubscriberFlags2=>{SubscriberFlags2[SubscriberFlags2["Computed"]=1]="Computed";SubscriberFlags2[SubscriberFlags2["Effect"]=2]="Effect";SubscriberFlags2[SubscriberFlags2["Tracking"]=4]="Tracking";SubscriberFlags2[SubscriberFlags2["Notified"]=8]="Notified";SubscriberFlags2[SubscriberFlags2["Recursed"]=16]="Recursed";SubscriberFlags2[SubscriberFlags2["Dirty"]=32]="Dirty";SubscriberFlags2[SubscriberFlags2["PendingComputed"]=64]="PendingComputed";SubscriberFlags2[SubscriberFlags2["PendingEffect"]=128]="PendingEffect";SubscriberFlags2[SubscriberFlags2["Propagated"]=224]="Propagated";return SubscriberFlags2})(SubscriberFlags||{});function createReactiveSystem({updateComputed,notifyEffect:notifyEffect2}){const notifyBuffer=[];let notifyIndex=0;let notifyBufferLength=0;return{link:link2,propagate:propagate2,updateDirtyFlag:updateDirtyFlag2,startTracking:startTracking2,endTracking:endTracking2,processEffectNotifications:processEffectNotifications2,processComputedUpdate:processComputedUpdate2,processPendingInnerEffects:processPendingInnerEffects2};function link2(dep,sub){const currentDep=sub.depsTail;if(currentDep!==void 0&&currentDep.dep===dep){return}const nextDep=currentDep!==void 0?currentDep.nextDep:sub.deps;if(nextDep!==void 0&&nextDep.dep===dep){sub.depsTail=nextDep;return}const depLastSub=dep.subsTail;if(depLastSub!==void 0&&depLastSub.sub===sub&&isValidLink(depLastSub,sub)){return}return linkNewDep(dep,sub,nextDep,currentDep)}function propagate2(current){let next=current.nextSub;let branchs;let branchDepth=0;let targetFlag=32;top:do{const sub=current.sub;const subFlags=sub.flags;let shouldNotify=false;if(!(subFlags&(4|16|224))){sub.flags=subFlags|targetFlag|8;shouldNotify=true}else if(subFlags&16&&!(subFlags&4)){sub.flags=subFlags&~16|targetFlag|8;shouldNotify=true}else if(!(subFlags&224)&&isValidLink(current,sub)){sub.flags=subFlags|16|targetFlag|8;shouldNotify=sub.subs!==void 0}if(shouldNotify){const subSubs=sub.subs;if(subSubs!==void 0){current=subSubs;if(subSubs.nextSub!==void 0){branchs={target:next,linked:branchs};++branchDepth;next=current.nextSub;targetFlag=64}else{targetFlag=subFlags&2?128:64}continue}if(subFlags&2){notifyBuffer[notifyBufferLength++]=sub}}else if(!(subFlags&(4|targetFlag))){sub.flags=subFlags|targetFlag|8;if((subFlags&(2|8))===2){notifyBuffer[notifyBufferLength++]=sub}}else if(!(subFlags&targetFlag)&&subFlags&224&&isValidLink(current,sub)){sub.flags=subFlags|targetFlag}if((current=next)!==void 0){next=current.nextSub;targetFlag=branchDepth?64:32;continue}while(branchDepth--){current=branchs.target;branchs=branchs.linked;if(current!==void 0){next=current.nextSub;targetFlag=branchDepth?64:32;continue top}}break}while(true)}function startTracking2(sub){sub.depsTail=void 0;sub.flags=sub.flags&~(8|16|224)|4}function endTracking2(sub){const depsTail=sub.depsTail;if(depsTail!==void 0){const nextDep=depsTail.nextDep;if(nextDep!==void 0){clearTracking(nextDep);depsTail.nextDep=void 0}}else if(sub.deps!==void 0){clearTracking(sub.deps);sub.deps=void 0}sub.flags&=~4}function updateDirtyFlag2(sub,flags){if(checkDirty(sub.deps)){sub.flags=flags|32;return true}else{sub.flags=flags&~64;return false}}function processComputedUpdate2(computed2,flags){if(flags&32||checkDirty(computed2.deps)){if(updateComputed(computed2)){const subs=computed2.subs;if(subs!==void 0){shallowPropagate(subs)}}}else{computed2.flags=flags&~64}}function processPendingInnerEffects2(sub,flags){if(flags&128){sub.flags=flags&~128;let link3=sub.deps;do{const dep=link3.dep;if("flags"in dep&&dep.flags&2&&dep.flags&224){notifyEffect2(dep)}link3=link3.nextDep}while(link3!==void 0)}}function processEffectNotifications2(){while(notifyIndex<notifyBufferLength){const effect2=notifyBuffer[notifyIndex];notifyBuffer[notifyIndex++]=void 0;if(!notifyEffect2(effect2)){effect2.flags&=~8}}notifyIndex=0;notifyBufferLength=0}function linkNewDep(dep,sub,nextDep,depsTail){const newLink={dep,sub,nextDep,prevSub:void 0,nextSub:void 0};if(depsTail===void 0){sub.deps=newLink}else{depsTail.nextDep=newLink}if(dep.subs===void 0){dep.subs=newLink}else{const oldTail=dep.subsTail;newLink.prevSub=oldTail;oldTail.nextSub=newLink}sub.depsTail=newLink;dep.subsTail=newLink;return newLink}function checkDirty(current){let prevLinks;let checkDepth=0;let dirty;top:do{dirty=false;const dep=current.dep;if(current.sub.flags&32){dirty=true}else if("flags"in dep){const depFlags=dep.flags;if((depFlags&(1|32))===(1|32)){if(updateComputed(dep)){const subs=dep.subs;if(subs.nextSub!==void 0){shallowPropagate(subs)}dirty=true}}else if((depFlags&(1|64))===(1|64)){if(current.nextSub!==void 0||current.prevSub!==void 0){prevLinks={target:current,linked:prevLinks}}current=dep.deps;++checkDepth;continue}}if(!dirty&&current.nextDep!==void 0){current=current.nextDep;continue}while(checkDepth){--checkDepth;const sub=current.sub;const firstSub=sub.subs;if(dirty){if(updateComputed(sub)){if(firstSub.nextSub!==void 0){current=prevLinks.target;prevLinks=prevLinks.linked;shallowPropagate(firstSub)}else{current=firstSub}continue}}else{sub.flags&=~64}if(firstSub.nextSub!==void 0){current=prevLinks.target;prevLinks=prevLinks.linked}else{current=firstSub}if(current.nextDep!==void 0){current=current.nextDep;continue top}dirty=false}return dirty}while(true)}function shallowPropagate(link3){do{const sub=link3.sub;const subFlags=sub.flags;if((subFlags&(64|32))===64){sub.flags=subFlags|32|8;if((subFlags&(2|8))===2){notifyBuffer[notifyBufferLength++]=sub}}link3=link3.nextSub}while(link3!==void 0)}function isValidLink(checkLink,sub){const depsTail=sub.depsTail;if(depsTail!==void 0){let link3=sub.deps;do{if(link3===checkLink){return true}if(link3===depsTail){break}link3=link3.nextDep}while(link3!==void 0)}return false}function clearTracking(link3){do{const dep=link3.dep;const nextDep=link3.nextDep;const nextSub=link3.nextSub;const prevSub=link3.prevSub;if(nextSub!==void 0){nextSub.prevSub=prevSub}else{dep.subsTail=prevSub}if(prevSub!==void 0){prevSub.nextSub=nextSub}else{dep.subs=nextSub}if(dep.subs===void 0&&"deps"in dep){const depFlags=dep.flags;if(!(depFlags&32)){dep.flags=depFlags|32}const depDeps=dep.deps;if(depDeps!==void 0){link3=depDeps;dep.depsTail.nextDep=nextDep;dep.deps=void 0;dep.depsTail=void 0;continue}}link3=nextDep}while(link3!==void 0)}}var{link,propagate,updateDirtyFlag,startTracking,endTracking,processEffectNotifications,processComputedUpdate,processPendingInnerEffects}=createReactiveSystem({updateComputed(computed2){const prevSub=activeSub;activeSub=computed2;startTracking(computed2);try{const oldValue=computed2.currentValue;const newValue=computed2.getter(oldValue);if(oldValue!==newValue){computed2.currentValue=newValue;return true}return false}finally{activeSub=prevSub;endTracking(computed2)}},notifyEffect(e){if("isScope"in e){return notifyEffectScope(e)}else{return notifyEffect(e)}}});var pauseStack=[];var batchDepth=0;var activeSub;var activeScope;function startBatch(){++batchDepth}function endBatch(){if(!--batchDepth){processEffectNotifications()}}function pauseTracking(){pauseStack.push(activeSub);activeSub=void 0}function resumeTracking(){activeSub=pauseStack.pop()}function signal(initialValue){return signalGetterSetter.bind({currentValue:initialValue,subs:void 0,subsTail:void 0})}function computed(getter){return computedGetter.bind({currentValue:void 0,subs:void 0,subsTail:void 0,deps:void 0,depsTail:void 0,flags:1|32,getter})}function effect(fn){const e={fn,subs:void 0,subsTail:void 0,deps:void 0,depsTail:void 0,flags:2};if(activeSub!==void 0){link(e,activeSub)}else if(activeScope!==void 0){link(e,activeScope)}const prevSub=activeSub;activeSub=e;try{e.fn()}finally{activeSub=prevSub}return effectStop.bind(e)}function effectScope(fn){const e={deps:void 0,depsTail:void 0,flags:2,isScope:true};const prevSub=activeScope;activeScope=e;try{fn()}finally{activeScope=prevSub}return effectStop.bind(e)}function notifyEffect(e){const flags=e.flags;if(flags&32||flags&64&&updateDirtyFlag(e,flags)){const prevSub=activeSub;activeSub=e;startTracking(e);try{e.fn()}finally{activeSub=prevSub;endTracking(e)}}else{processPendingInnerEffects(e,e.flags)}return true}function notifyEffectScope(e){const flags=e.flags;if(flags&128){processPendingInnerEffects(e,e.flags);return true}return false}function computedGetter(){const flags=this.flags;if(flags&(32|64)){processComputedUpdate(this,flags)}if(activeSub!==void 0){link(this,activeSub)}else if(activeScope!==void 0){link(this,activeScope)}return this.currentValue}function signalGetterSetter(...value){if(value.length){if(this.currentValue!==(this.currentValue=value[0])){const subs=this.subs;if(subs!==void 0){propagate(subs);if(!batchDepth){processEffectNotifications()}}}}else{if(activeSub!==void 0){link(this,activeSub)}return this.currentValue}}function effectStop(){startTracking(this);endTracking(this)}export{SubscriberFlags,batchDepth,computed,createReactiveSystem,effect,effectScope,endBatch,pauseTracking,resumeTracking,signal,startBatch};
