# 量化积分管理系统 2.0 - 前端

基于 Vue 3 + TypeScript + Vite 构建的现代化前端应用。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - JavaScript 的超集，提供静态类型检查
- **Vite** - 下一代前端构建工具
- **Vue Router 4** - Vue.js 官方路由管理器
- **Node.js 22+** - JavaScript 运行时环境
- **pnpm** - 快速、节省磁盘空间的包管理器

## 项目特性

- ⚡ 快速的热重载开发体验
- 📱 响应式设计，适配PC端
- 🎯 TypeScript 支持，提供更好的开发体验
- 🛠️ 现代化的构建工具链
- 📦 优化的包管理

## 开发环境要求

- Node.js 22 或更高版本
- pnpm 包管理器

## 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动开发服务器

```bash
pnpm dev
```

项目将在 http://localhost:8002 启动

### 3. 构建生产版本

```bash
pnpm build
```

### 4. 预览生产构建

```bash
pnpm preview
```

## 项目结构

```
src/
├── components/     # 可复用组件
├── views/         # 页面组件
├── router/        # 路由配置
├── assets/        # 静态资源
├── App.vue        # 根组件
└── main.ts        # 应用入口
```

## 开发说明

- 使用 Vue 3 Composition API 和 `<script setup>` 语法
- 所有组件使用 TypeScript 编写
- 路由使用 Vue Router 4
- 样式使用 Scoped CSS

## 浏览器支持

支持所有现代浏览器，包括：
- Chrome 87+
- Firefox 78+
- Safari 14+
- Edge 88+
